/home/<USER>/miniconda3/envs/RoboTwin_framepack/bin/python -m scripts.agilex_inference_realworld \
      --use_actions_interpolation \
      --pretrained_model_name_or_path=/home/<USER>/Checkpoints/ckpt_clean/real_world_pick_carrot_1b_gpu0_globalbs64_20251105_170129_no_lora/checkpoint-10000 \
      --lang_embeddings_path=/home/<USER>/Codes/Robotwin_framepack/data/close_laptop.pt \
      --hunyuan_text_embeds_path=/home/<USER>/Codes/Robotwin_framepack/data/close_laptop_framepack_text_embed.pkl \
      --config_path=configs/test_fft_realworld_eval/dual_stream_fft_sobel_realworld_1b.yaml \
      --ctrl_freq=30



